import { SynchronyValidationRequest } from '~/data/models/SynchronyValidationRequest';
import { SynchronyValidationResponse } from '~/data/models/SynchronyValidationResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';
import { ApiRequest } from '~/lib/fetch-backend/index.types';

export async function backendGetSynchronySession(
  {
    cartId,
    input,
  }: {
    cartId: string;
    input: SynchronyValidationRequest;
  },
  request?: ApiRequest,
) {
  return await fetchWithErrorHandling<
    SynchronyValidationResponse,
    SynchronyValidationRequest
  >({
    endpoint: '/v2/site/payment/session/{cartId}',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    jsonBody: input,
    method: 'post',
    params: {
      cartId,
    },
    request,
  });
}
